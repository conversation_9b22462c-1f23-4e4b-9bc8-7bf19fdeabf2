<template>
  <div class="early-access-form">
    <!-- Different content based on authentication status -->
    <template v-if="!isAuthenticated">
      <!-- Compact Category Dropdown -->
      <div class="category-dropdown-section q-mb-md">
        <q-select
          v-model="selectedCategory"
          :options="categoryOptions"
          option-value="id"
          option-label="label"
          emit-value
          map-options
          outlined
          label="Choose your category"
          class="compact-category-select"
          dense
        >
          <template v-slot:prepend>
            <q-icon name="person" color="primary" />
          </template>
          <template v-slot:option="scope">
            <q-item v-bind="scope.itemProps" class="compact-option">
              <q-item-section avatar>
                <q-icon :name="scope.opt.icon" :color="getCategoryColor(scope.opt.id)" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ scope.opt.label }}</q-item-label>
                <q-item-label caption>{{ scope.opt.description }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
        </q-select>
      </div>

      <!-- Email Sign Up Form - Show when category selected -->
      <div v-if="selectedCategory" class="email-signup-form">
        <q-form @submit="handleEmailPasswordSignup" class="q-gutter-md">
          <q-input
            v-model="form.email"
            type="email"
            label="Email"
            outlined
            dense
            :rules="emailRules"
          >
            <template v-slot:prepend>
              <q-icon name="email" color="primary" />
            </template>
          </q-input>

          <q-input
            v-model="form.password"
            :type="isPwd ? 'password' : 'text'"
            label="Password"
            outlined
            dense
            :rules="passwordRules"
          >
            <template v-slot:prepend>
              <q-icon name="lock" color="primary" />
            </template>
            <template v-slot:append>
              <q-icon
                :name="isPwd ? 'visibility_off' : 'visibility'"
                class="cursor-pointer"
                @click="isPwd = !isPwd"
                color="grey-6"
              />
            </template>
          </q-input>

          <q-checkbox
            v-model="form.acceptTerms"
            color="primary"
            dense
          >
            <span class="text-caption">
              I agree to the
              <router-link to="/legal/terms-conditions" target="_blank" class="text-primary">
                Terms
              </router-link>
              and
              <router-link to="/legal/privacy-policy" target="_blank" class="text-primary">
                Privacy Policy
              </router-link>
            </span>
          </q-checkbox>

          <q-btn
            type="submit"
            label="Create Account"
            :loading="loading"
            :disable="!form.acceptTerms || !selectedCategory"
            class="full-width"
            color="primary"
            no-caps
            rounded
          />
        </q-form>
      </div>

      <!-- Message when no category selected -->
      <div v-else class="no-category-message">
        <p class="text-caption text-center text-grey-6">
          Please select your category above to continue with sign up
        </p>
      </div>


    </template>

    <!-- Message for logged-in users -->
    <template v-else>
      <div class="text-center q-pa-md">
        <template v-if="profileCompletion < 100">
          <p class="text-body1 q-mb-md">Complete your profile to enhance matchmaking with other innovators, gain personalized recommendations, and maximize your networking opportunities within our ecosystem.</p>
          <q-btn
            color="primary"
            label="Complete Your Profile"
            class="q-mt-md cta-button"
            @click="goToProfileEdit"
            size="md"
            rounded
          />
        </template>
        <template v-else>
          <p class="text-body1 q-mb-md">Your complete profile helps us connect you with the right partners, resources, and opportunities. Explore the dashboard to start networking and collaborating with other ecosystem members.</p>
          <q-btn
            color="primary"
            label="Go to Dashboard"
            class="q-mt-md"
            @click="goToDashboard"
            size="lg"
          />
        </template>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAuthStore } from '../../stores/auth'
import { useRouter } from 'vue-router'
import { useNotificationStore } from '../../stores/notifications'
import { useProfileStore } from '../../stores/profile'
import UnifiedIcon from '../ui/UnifiedIcon.vue'
import { useCategoryService, type CategoryOption } from '@/services/categoryService'

const router = useRouter()
const authStore = useAuthStore()
const profileStore = useProfileStore()
const notificationStore = useNotificationStore()
const { getCategories } = useCategoryService()

const selectedCategory = ref<string | null>(null)
const loading = ref(false)
const isPwd = ref(true)

// Get category options
const categoryOptions = getCategories()

// Form data
const form = ref({
  email: '',
  password: '',
  acceptTerms: false
})

// Check if user is authenticated
const isAuthenticated = computed(() => authStore.isAuthenticated)

// Get profile completion percentage
const profileCompletion = computed(() => profileStore.profileCompletion)

// Navigation functions
const goToDashboard = () => {
  router.push('/dashboard')
}

const goToProfileEdit = () => {
  router.push('/dashboard/profile')
}

// Color palette for categories
const categoryColors = {
  innovator: '#FF6B6B',
  mentor: '#4ECDC4',
  investor: '#45B7D1',
  industry_expert: '#96CEB4',
  academic: '#FFEAA7',
  student: '#DDA0DD',
  academic_institution: '#98D8C8',
  government_organisation: '#F7DC6F'
}

const getCategoryColor = (categoryId: string): string => {
  return categoryColors[categoryId as keyof typeof categoryColors] || '#0D8A3E'
}

// Validation rules
const emailRules = [
  (val: string) => !!val || 'Email is required',
  (val: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) || 'Please enter a valid email'
]

const passwordRules = [
  (val: string) => !!val || 'Password is required',
  (val: string) => val.length >= 6 || 'Password must be at least 6 characters'
]

// Handle email/password signup
const handleEmailPasswordSignup = async () => {
  try {
    loading.value = true

    // Basic validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!form.value.email || !emailRegex.test(form.value.email)) {
      throw new Error('Please enter a valid email address')
    }

    if (form.value.password.length < 6) {
      throw new Error('Password must be at least 6 characters')
    }

    console.log('Preparing to sign up with:', {
      email: form.value.email,
      category: selectedCategory.value
    })

    // Use auth store for signup
    await authStore.signUp({
      email: form.value.email,
      password: form.value.password
    })

    // Reset form
    form.value = {
      email: '',
      password: '',
      acceptTerms: false
    }
    selectedCategory.value = null

  } catch (error: any) {
    console.error('Email password signup error:', error)
    notificationStore.error(error.message || 'Registration failed. Please try again.')
  } finally {
    loading.value = false
  }
}


</script>

<style scoped>
/* Base styles */
.early-access-form {
  width: 100%;
}

/* Form element styles */
.q-form {
  gap: 1rem !important;
}

.custom-btn {
  min-width: 200px;
}

.cta-button {
  border-radius: 25px !important;
  min-width: 160px;
  height: 40px;
  font-weight: 600;
}

.icon {
  font-size: 20px;
  display: flex;
  align-items: center;
}

.icon-arrow-down {
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;
}

/* Deep selectors for Quasar components */
:deep(.q-field) {
  width: 100%;
}

:deep(.q-input), :deep(.q-select) {
  width: 100%;
}

:deep(.q-field__control) {
  width: 100%;
}

:deep(.row) {
  margin: 0;
}

:deep(.col-sm-12) {
  padding: 0;
}

:deep(.q-field--focused) .icon-arrow-down {
  transform: rotate(180deg);
}

:deep(.q-select .q-field__append) {
  padding-right: 8px;
  height: 100%;
  display: flex;
  align-items: center;
}

:deep(.q-select .q-field__append .q-icon) {
  display: none;
}

:deep(.q-select .q-field__append .icon) {
  opacity: 0.7;
}

:deep(.q-field--focused .q-field__append .icon) {
  opacity: 1;
}

/* SVG icon styling */
:deep(svg) {
  width: 20px;
  height: 20px;
  stroke: currentColor;
}

:deep(.q-field__control-item svg) {
  color: #0D8A3E;
}

/* Compact category dropdown */
.category-dropdown-section {
  max-width: 400px;
  margin: 0 auto;
}

.compact-category-select {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.compact-category-select :deep(.q-field__control) {
  border-radius: 12px;
  background: transparent;
}

.compact-option {
  padding: 8px 12px;
}

.compact-option:hover {
  background: rgba(13, 138, 62, 0.1);
}

/* Email signup form */
.email-signup-form {
  max-width: 400px;
  margin: 1rem auto;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.3s ease;
}

.email-signup-form :deep(.q-field__control) {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.email-signup-form :deep(.q-btn) {
  background: linear-gradient(135deg, #0D8A3E 0%, #0F9B47 100%);
  box-shadow: 0 2px 8px rgba(13, 138, 62, 0.3);
  transition: all 0.3s ease;
}

.email-signup-form :deep(.q-btn:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(13, 138, 62, 0.4);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.no-category-message {
  max-width: 400px;
  margin: 1rem auto;
  padding: 0.5rem;
}

/* Responsive styles */
@media (max-width: 599px) {
  .early-access-form {
    padding: 0;
  }

  .custom-btn {
    width: 100%;
  }

  .category-dropdown-section,
  .email-signup-form {
    max-width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  .email-signup-form {
    padding: 1rem;
  }
}
</style>
