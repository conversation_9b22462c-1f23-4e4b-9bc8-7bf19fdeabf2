<template>
  <div class="smile-factory-landing">
    <!-- Navigation -->
    <nav class="navigation">
      <div class="nav-container">
        <router-link to="/" class="logo">
          <img src="@/assets/logo/ZBFH_SmileFactory Logo_1.png" alt="Smile Factory" class="logo-img" />
        </router-link>
        
        <!-- Desktop Navigation -->
        <div class="nav-buttons desktop-nav">
          <button class="btn-secondary" @click="handleSignIn">Sign In</button>
          <button class="btn-primary" @click="handleJoinNow">Join Now</button>
        </div>
        
        <!-- Mobile Navigation -->
        <button class="mobile-menu-button mobile-nav" @click="toggleMobileMenu">
          <span class="sr-only">Open main menu</span>
          <svg class="hamburger-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-content">
        <div class="startup-badge">
          <svg class="badge-icon" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
          25+ Startups Launched
        </div>
        
        <h1 class="hero-title">
          Bringing you a vibrant ecosystem<br>
          that fosters innovation
        </h1>
        
        <button class="btn-large">
          Join the Community Today
          <svg class="arrow-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14m-7-7l7 7-7 7"/>
          </svg>
        </button>
      </div>
    </section>

    <!-- Who We Are Section -->
    <section class="content-section">
      <div class="container">
        <h2 class="section-heading who-we-are">Who We are</h2>
        <p class="section-description">
          We create a unique environment where innovators, entrepreneurs, and industry leaders collaborate to drive meaningful change.
        </p>
      </div>
    </section>

    <!-- Our Mandate Section -->
    <section class="content-section">
      <div class="container">
        <h2 class="section-heading our-mandate">Our Mandate</h2>
        <p class="section-description">
          We actively connect and support all stakeholders in the innovation ecosystem, ensuring seamless collaboration and sustainable growth.
        </p>
      </div>
    </section>

    <!-- The Goal Section -->
    <section class="goal-section">
      <div class="container">
        <h1 class="section-heading the-goal">The Goal</h1>
        <p class="goal-description">
          Smile Factory connects all stakeholders in the innovation ecosystem, creating a powerful network of collaboration and growth.
        </p>
        
        <div class="goal-cards">
          <div class="goal-card" @click="selectCard('connect')" :class="{ active: selectedCard === 'connect' }">
            <h3>Connect</h3>
            <div class="card-content">
              <h3>Connect</h3>
              <p>We bridge innovators, startups, corporates, investors, and institutions, fostering meaningful connections that spark opportunity and exchange.</p>
            </div>
          </div>
          
          <div class="goal-card" @click="selectCard('collaborate')" :class="{ active: selectedCard === 'collaborate' }">
            <h3>Collaborate</h3>
            <div class="card-content">
              <h3>Collaborate</h3>
              <p>We enable cross-sector collaboration through curated programs, shared workspaces, and open innovation platforms that accelerate problem-solving and co-creation.</p>
            </div>
          </div>
          
          <div class="goal-card" @click="selectCard('grow')" :class="{ active: selectedCard === 'grow' }">
            <h3>Grow</h3>
            <div class="card-content">
              <h3>Grow</h3>
              <p>We support growth through mentorship, funding access, market exposure, and strategic guidance that empowers ideas to scale into impactful ventures.</p>
            </div>
          </div>
        </div>
        
        <button class="btn-large">
          Join the Community Today
          <svg class="arrow-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14m-7-7l7 7-7 7"/>
          </svg>
        </button>
      </div>
    </section>

    <!-- Community Section -->
    <section class="community-section">
      <div class="container">
        <h2 class="section-heading community-heading">Smile Factory Community</h2>
        <h3 class="community-subheading">
          A unified digital platform connecting innovators, mentors, students, government, academia, and funders in one intelligent workspace.
        </h3>
        <p class="community-description">
          Through smart matchmaking algorithms and integrated collaboration tools, we seamlessly connect each stakeholder with the right opportunities, resources, and partnerships—creating a thriving ecosystem where every participant contributes to and benefits from collective innovation success.
        </p>
        
        <button class="btn-large">
          Join the Community Today
          <svg class="arrow-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14m-7-7l7 7-7 7"/>
          </svg>
        </button>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="footer-content">
        <div class="footer-main">
          <div class="footer-brand">
            <img src="@/assets/logo/ZBFH_SmileFactory Logo_1.png" alt="Smile Factory" class="footer-logo" />
            <p class="footer-description">
              Connecting all stakeholders in the innovation ecosystem to create a powerful network of collaboration and growth.
            </p>
            <div class="footer-contact">
              <svg class="email-icon" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
              </svg>
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </div>
          </div>
          
          <div class="footer-links">
            <div class="footer-column">
              <h3>Platform</h3>
              <ul>
                <li><a href="#who-we-are">Who We Are</a></li>
                <li><a href="#our-mandate">Our Mandate</a></li>
                <li><a href="#the-community">The Community</a></li>
                <li><a href="#success-stories">Success Stories</a></li>
                <li><a href="#how-it-works">How It Works</a></li>
              </ul>
            </div>
            
            <div class="footer-column">
              <h3>Join As</h3>
              <ul>
                <li><a href="/register?type=innovator">Entrepreneur</a></li>
                <li><a href="/register?type=mentor">Mentor</a></li>
                <li><a href="/register?type=student">Student</a></li>
                <li><a href="/register?type=funder">Investor</a></li>
                <li><a href="/register?type=academic">Institution</a></li>
                <li><a href="/register?type=expert">Expert</a></li>
              </ul>
            </div>
            
            <div class="footer-column">
              <h3>Resources</h3>
              <ul>
                <li><a href="/resources/getting-started">Getting Started Guide</a></li>
                <li><a href="/resources/best-practices">Best Practices</a></li>
                <li><a href="/resources/guidelines">Community Guidelines</a></li>
                <li><a href="/help">Help Center</a></li>
                <li><a href="/blog">Blog</a></li>
              </ul>
            </div>
          </div>
        </div>
        
        <div class="newsletter-section">
          <h3>Stay Connected</h3>
          <p>Get the latest updates on platform features, success stories, and innovation trends.</p>
          <div class="newsletter-form">
            <input type="email" placeholder="Enter your email" class="email-input" v-model="email" />
            <button class="btn-primary">Subscribe</button>
          </div>
        </div>
      </div>
      
      <div class="footer-bottom">
        <div class="footer-bottom-content">
          <p>&copy; 2025 Smile Factory</p>
          <div class="legal-links">
            <a href="/privacy">Privacy Policy</a>
            <a href="/terms">Terms of Service</a>
            <a href="/cookies">Cookie Policy</a>
            <a href="/data-protection">Data Protection</a>
          </div>
        </div>
        
        <div class="social-links">
          <span>Follow us:</span>
          <a href="https://linkedin.com/company/smile-factory" aria-label="Follow us on LinkedIn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
            </svg>
          </a>
          <a href="https://twitter.com/SmileFactoryHQ" aria-label="Follow us on Twitter">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
            </svg>
          </a>
          <a href="https://youtube.com/SmileFactoryPlatform" aria-label="Follow us on YouTube">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
            </svg>
          </a>
        </div>
      </div>
    </footer>

    <!-- Auth Dialogs -->
    <UnifiedAuthDialogs />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useUnifiedAuth } from '@/services/unifiedAuthService'
import UnifiedAuthDialogs from '@/components/auth/UnifiedAuthDialogs.vue'

// Services
const { openSignUpDialog, openSignInDialog } = useUnifiedAuth()




// Reactive data
const selectedCard = ref('connect')
const email = ref('')
const mobileMenuOpen = ref(false)

// Methods
const selectCard = (card: string) => {
  selectedCard.value = card
}

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const handleSignIn = () => {
  openSignInDialog()
}

const handleJoinNow = () => {
  openSignUpDialog()
}
</script>

<style scoped>
/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Sora:wght@400;600&family=Plus+Jakarta+Sans:wght@400;500;600&display=swap');

/* Global Styles */
.smile-factory-landing {
  font-family: 'Plus Jakarta Sans', sans-serif;
  line-height: 1.6;
  color: #000;
  overflow-x: hidden;
}

/* Navigation Styles */
.navigation {
  position: fixed;
  top: 80px;
  width: 100%;
  z-index: 50;
  transition: all 100ms ease;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 16px 0;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.logo-img {
  height: 40px;
  width: auto;
}

.nav-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
}

.mobile-menu-button {
  display: none;
  padding: 8px;
  border-radius: 6px;
  background: transparent;
  border: none;
  cursor: pointer;
  color: #374151;
}

.hamburger-icon {
  width: 24px;
  height: 24px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Button Styles */
.btn-primary {
  background-color: rgb(131, 186, 38);
  color: white;
  padding: 8px 24px;
  border-radius: 9999px;
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: rgb(111, 156, 28);
  transform: translateY(-1px);
}

.btn-secondary {
  background: transparent;
  color: rgb(131, 186, 38);
  padding: 8px 16px;
  border: none;
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 6px;
}

.btn-secondary:hover {
  color: rgb(111, 156, 28);
  background: rgba(131, 186, 38, 0.1);
}

.btn-large {
  background: transparent;
  color: white;
  padding: 16px 32px;
  border: 2px solid white;
  border-radius: 9999px;
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 18px;
  font-weight: 400;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn-large:hover {
  background: white;
  color: rgb(131, 186, 38);
  transform: translateY(-2px);
}

.arrow-icon {
  transition: transform 0.3s ease;
}

.btn-large:hover .arrow-icon {
  transform: translateX(4px);
}

/* Hero Section */
.hero-section {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(0, 106, 57, 0.95) 0%, rgba(131, 186, 38, 0.9) 100%),
              url('@/assets/hero/business-concept-business-people-shaking-hands-showing-mutual-agreement-betweent-their-companies-firms.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  color: white;
  text-align: center;
  position: relative;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 24px;
  z-index: 2;
}

.startup-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 24px;
  font-size: 14px;
  color: white;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.badge-icon {
  width: 16px;
  height: 16px;
}

.hero-title {
  font-family: 'Sora', sans-serif;
  font-size: 60px;
  font-weight: 600;
  line-height: 60px;
  color: white;
  margin-bottom: 32px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Content Sections */
.content-section {
  padding: 48px 0;
  display: flex;
  align-items: center;
  min-height: 400px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
}

.section-heading {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 18px;
  font-weight: 600;
  line-height: 28px;
  margin-bottom: 16px;
}

.who-we-are {
  color: #374151;
}

.our-mandate {
  color: rgb(131, 186, 38);
}

.the-goal {
  color: #374151;
}

.section-description {
  font-family: 'Sora', sans-serif;
  font-size: 48px;
  font-weight: 600;
  line-height: 48px;
  color: rgb(131, 186, 38);
  max-width: 800px;
  margin: 0 auto;
}

/* Goal Section */
.goal-section {
  padding: 48px 0;
  background: linear-gradient(135deg, rgba(0, 106, 57, 0.95) 0%, rgba(131, 186, 38, 0.9) 100%),
              url('@/assets/hero/group-afro-americans-working-together%20(1).jpg');
  background-size: cover;
  background-position: center;
  color: white;
  text-align: center;
}

.goal-description {
  font-family: 'Sora', sans-serif;
  font-size: 48px;
  font-weight: 600;
  line-height: 48px;
  color: rgb(0, 106, 57);
  max-width: 800px;
  margin: 0 auto 40px;
}

.goal-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin: 40px 0;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.goal-card {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 12px;
  padding: 32px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.goal-card:hover {
  transform: translateY(-4px);
  background: rgba(0, 0, 0, 0.7);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.goal-card h3 {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 24px;
  font-weight: 600;
  line-height: 32px;
  color: white;
  margin-bottom: 0;
}

.card-content {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 32px;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: opacity 0.3s ease;
}

.goal-card:hover .card-content {
  opacity: 1;
}

.card-content h3 {
  margin-bottom: 16px;
}

.card-content p {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  color: white;
  margin: 0;
}

/* Community Section */
.community-section {
  padding: 48px 0;
  background: white;
  text-align: center;
}

.community-heading {
  color: #374151;
  margin-bottom: 24px;
}

.community-subheading {
  font-family: 'Sora', sans-serif;
  font-size: 48px;
  font-weight: 600;
  line-height: 48px;
  color: rgb(131, 186, 38);
  max-width: 900px;
  margin: 0 auto 24px;
}

.community-description {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 18px;
  font-weight: 400;
  line-height: 28px;
  color: #374151;
  max-width: 800px;
  margin: 0 auto 40px;
}

/* Footer */
.footer {
  background-color: #1f2937;
  color: #d1d5db;
  padding: 48px 0 24px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.footer-main {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 48px;
  margin-bottom: 48px;
}

.footer-brand {
  max-width: 400px;
}

.footer-logo {
  height: 40px;
  width: auto;
  margin-bottom: 16px;
  filter: brightness(0) invert(1);
}

.footer-description {
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 16px;
  color: #9ca3af;
}

.footer-contact {
  display: flex;
  align-items: center;
  gap: 8px;
}

.email-icon {
  width: 16px;
  height: 16px;
  color: rgb(131, 186, 38);
}

.footer-contact a {
  color: #d1d5db;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-contact a:hover {
  color: rgb(131, 186, 38);
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
}

.footer-column h3 {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  color: white;
  margin-bottom: 16px;
}

.footer-column ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-column li {
  margin-bottom: 8px;
}

.footer-column a {
  color: #9ca3af;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-column a:hover {
  color: rgb(131, 186, 38);
}

.newsletter-section {
  background: #374151;
  padding: 32px;
  border-radius: 12px;
  margin-bottom: 32px;
  text-align: center;
}

.newsletter-section h3 {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  color: white;
  margin-bottom: 8px;
}

.newsletter-section p {
  color: #9ca3af;
  margin-bottom: 24px;
  font-size: 14px;
}

.newsletter-form {
  display: flex;
  gap: 12px;
  max-width: 400px;
  margin: 0 auto;
}

.email-input {
  flex: 1;
  padding: 8px 16px;
  border: 1px solid #4b5563;
  border-radius: 6px;
  background: #1f2937;
  color: white;
  font-size: 14px;
}

.email-input::placeholder {
  color: #9ca3af;
}

.email-input:focus {
  outline: none;
  border-color: rgb(131, 186, 38);
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding-top: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-bottom-content {
  display: flex;
  align-items: center;
  gap: 24px;
}

.footer-bottom p {
  margin: 0;
  font-size: 14px;
  color: #9ca3af;
}

.legal-links {
  display: flex;
  gap: 16px;
}

.legal-links a {
  color: #9ca3af;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.legal-links a:hover {
  color: rgb(131, 186, 38);
}

.social-links {
  display: flex;
  align-items: center;
  gap: 12px;
}

.social-links span {
  font-size: 14px;
  color: #9ca3af;
}

.social-links a {
  color: #9ca3af;
  transition: color 0.3s ease;
}

.social-links a:hover {
  color: rgb(131, 186, 38);
}

.social-links svg {
  width: 20px;
  height: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .desktop-nav {
    display: none;
  }

  .mobile-nav {
    display: block;
  }

  .hero-title {
    font-size: 36px;
    line-height: 40px;
  }

  .section-description,
  .goal-description,
  .community-subheading {
    font-size: 28px;
    line-height: 32px;
  }

  .goal-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .footer-main {
    grid-template-columns: 1fr;
    gap: 32px;
  }

  .footer-links {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .newsletter-form {
    flex-direction: column;
  }

  .navigation {
    top: 0;
    padding: 12px 0;
  }

  .nav-container {
    padding: 0 16px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 28px;
    line-height: 32px;
  }

  .section-description,
  .goal-description,
  .community-subheading {
    font-size: 24px;
    line-height: 28px;
  }

  .btn-large {
    padding: 12px 24px;
    font-size: 16px;
  }

  .goal-card {
    padding: 24px;
  }

  .card-content {
    padding: 24px;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
