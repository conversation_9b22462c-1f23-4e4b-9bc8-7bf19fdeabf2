/**
 * Category Service
 * 
 * Manages user category selection for the multi-step registration flow.
 * Handles category data, localStorage persistence, and profile type mapping.
 */

export interface CategoryOption {
  id: string
  label: string
  description: string
  icon: string
  profileType: string
}

export const categoryOptions: CategoryOption[] = [
  {
    id: 'innovator',
    label: 'Innovator',
    description: 'Entrepreneurs and startups with innovative ideas',
    icon: 'lightbulb',
    profileType: 'innovator'
  },
  {
    id: 'mentor',
    label: 'Mentor',
    description: 'Experienced professionals offering guidance',
    icon: 'school',
    profileType: 'mentor'
  },
  {
    id: 'investor',
    label: 'Investor',
    description: 'Angel investors and VCs looking for opportunities',
    icon: 'attach_money',
    profileType: 'investor'
  },
  {
    id: 'industry_expert',
    label: 'Industry Expert',
    description: 'Subject matter experts with specialized knowledge',
    icon: 'engineering',
    profileType: 'industry_expert'
  },
  {
    id: 'academic',
    label: 'Organisation',
    description: 'Government and private organisations',
    icon: 'school',
    profileType: 'professional'
  },
  {
    id: 'student',
    label: 'Academic Student',
    description: 'Students looking for opportunities and connections',
    icon: 'person',
    profileType: 'academic_student'
  },
  {
    id: 'academic_institution',
    label: 'Academic Institution',
    description: 'Universities, colleges, and research institutions',
    icon: 'business',
    profileType: 'academic_institution'
  },
  {
    id: 'government_organisation',
    label: 'Government Organisation',
    description: 'Government agencies and public sector organizations',
    icon: 'account_balance',
    profileType: 'organisation'
  }
]

const STORAGE_KEY = 'selectedCategory'

/**
 * Category Service Class
 */
export class CategoryService {
  /**
   * Get all available category options
   */
  getCategories(): CategoryOption[] {
    return categoryOptions
  }

  /**
   * Get a specific category by ID
   */
  getCategoryById(id: string): CategoryOption | undefined {
    return categoryOptions.find(category => category.id === id)
  }

  /**
   * Save selected category to localStorage
   */
  saveSelectedCategory(categoryId: string): void {
    try {
      localStorage.setItem(STORAGE_KEY, categoryId)
    } catch (error) {
      console.warn('Failed to save category to localStorage:', error)
    }
  }

  /**
   * Get selected category from localStorage
   */
  getSelectedCategory(): CategoryOption | null {
    try {
      const categoryId = localStorage.getItem(STORAGE_KEY)
      if (categoryId) {
        return this.getCategoryById(categoryId) || null
      }
    } catch (error) {
      console.warn('Failed to get category from localStorage:', error)
    }
    return null
  }

  /**
   * Clear selected category from localStorage
   */
  clearSelectedCategory(): void {
    try {
      localStorage.removeItem(STORAGE_KEY)
    } catch (error) {
      console.warn('Failed to clear category from localStorage:', error)
    }
  }

  /**
   * Get profile type for a category
   */
  getProfileTypeForCategory(categoryId: string): string | null {
    const category = this.getCategoryById(categoryId)
    return category ? category.profileType : null
  }

  /**
   * Validate if a category ID is valid
   */
  isValidCategory(categoryId: string): boolean {
    return categoryOptions.some(category => category.id === categoryId)
  }

  /**
   * Get category display name
   */
  getCategoryDisplayName(categoryId: string): string {
    const category = this.getCategoryById(categoryId)
    return category ? category.label : 'Unknown Category'
  }

  /**
   * Get category description
   */
  getCategoryDescription(categoryId: string): string {
    const category = this.getCategoryById(categoryId)
    return category ? category.description : ''
  }

  /**
   * Get category icon
   */
  getCategoryIcon(categoryId: string): string {
    const category = this.getCategoryById(categoryId)
    return category ? category.icon : 'help'
  }
}

// Export singleton instance
export const categoryService = new CategoryService()

/**
 * Composable for using category service in Vue components
 */
export function useCategoryService() {
  return {
    categoryService,
    categoryOptions,
    
    // Convenience methods
    getCategories: () => categoryService.getCategories(),
    getCategoryById: (id: string) => categoryService.getCategoryById(id),
    saveSelectedCategory: (id: string) => categoryService.saveSelectedCategory(id),
    getSelectedCategory: () => categoryService.getSelectedCategory(),
    clearSelectedCategory: () => categoryService.clearSelectedCategory(),
    getProfileTypeForCategory: (id: string) => categoryService.getProfileTypeForCategory(id),
    isValidCategory: (id: string) => categoryService.isValidCategory(id),
    getCategoryDisplayName: (id: string) => categoryService.getCategoryDisplayName(id),
    getCategoryDescription: (id: string) => categoryService.getCategoryDescription(id),
    getCategoryIcon: (id: string) => categoryService.getCategoryIcon(id)
  }
}

export default categoryService
